'use client';

import React from 'react';
import { ChatMessage as ChatMessageType } from '@/types/chat';
import { Bot, User } from 'lucide-react';
import ResourceCard from '@/components/resource/ResourceCard';

interface ChatMessageProps {
  message: ChatMessageType;
}

const ChatMessage: React.FC<ChatMessageProps> = ({ message }) => {
  const isUser = message.sender === 'user';
  const isAI = message.sender === 'ai';

  return (
    <div className={`flex w-full mb-6 ${isUser ? 'justify-end' : 'justify-start'}`}>
      <div className={`flex max-w-[85%] sm:max-w-[80%] ${isUser ? 'flex-row-reverse' : 'flex-row'} gap-2 sm:gap-3`}>
        {/* Avatar */}
        <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
          isUser 
            ? 'bg-indigo-600 text-white' 
            : 'bg-gray-100 text-gray-600'
        }`}>
          {isUser ? (
            <User className="w-4 h-4" />
          ) : (
            <Bot className="w-4 h-4" />
          )}
        </div>

        {/* Message Content */}
        <div className={`flex flex-col ${isUser ? 'items-end' : 'items-start'}`}>
          {/* Message Bubble */}
          <div className={`px-3 sm:px-4 py-2 sm:py-3 rounded-2xl max-w-full ${
            isUser
              ? 'bg-indigo-600 text-white rounded-br-md'
              : 'bg-gray-100 text-gray-900 rounded-bl-md'
          }`}>
            <p className="text-sm leading-relaxed whitespace-pre-wrap break-words">
              {message.text}
            </p>
          </div>

          {/* Timestamp */}
          <div className={`text-xs text-gray-500 mt-1 ${isUser ? 'text-right' : 'text-left'}`}>
            {message.timestamp.toLocaleTimeString([], { 
              hour: '2-digit', 
              minute: '2-digit' 
            })}
          </div>

          {/* Recommended Entities (only for AI messages) */}
          {isAI && message.recommendedEntities && message.recommendedEntities.length > 0 && (
            <div className="mt-4 w-full">
              <h4 className="text-sm font-medium text-gray-700 mb-3">
                Recommended for you:
              </h4>
              <div className="grid gap-3 sm:gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-1">
                {message.recommendedEntities.map((entity) => (
                  <div key={entity.id} className="transform scale-90 sm:scale-95">
                    <ResourceCard entity={entity} />
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Error State */}
          {message.metadata?.error && (
            <div className="mt-2 px-3 py-2 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-sm text-red-600">
                Error: {message.metadata.error}
              </p>
            </div>
          )}

          {/* Loading State */}
          {message.metadata?.isLoading && (
            <div className="mt-2 flex items-center gap-2 text-gray-500">
              <div className="w-4 h-4 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin"></div>
              <span className="text-sm">Processing...</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ChatMessage;
