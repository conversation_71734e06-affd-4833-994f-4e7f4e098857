'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Send, Loader2 } from 'lucide-react';

interface ChatInputProps {
  userInput: string;
  setUserInput: (value: string) => void;
  onSubmit: () => void;
  isLoading: boolean;
  placeholder?: string;
  disabled?: boolean;
}

const ChatInput: React.FC<ChatInputProps> = ({
  userInput,
  setUserInput,
  onSubmit,
  isLoading,
  placeholder = "Ask me about AI tools, courses, or anything else...",
  disabled = false,
}) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [isFocused, setIsFocused] = useState(false);

  // Auto-resize textarea based on content
  const adjustTextareaHeight = () => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = `${Math.min(textarea.scrollHeight, 120)}px`;
    }
  };

  useEffect(() => {
    adjustTextareaHeight();
  }, [userInput]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!userInput.trim() || isLoading || disabled) return;
    onSubmit();
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const canSubmit = userInput.trim().length > 0 && !isLoading && !disabled;

  return (
    <div className="border-t border-gray-200 bg-white px-3 sm:px-4 py-3 sm:py-4">
      <form onSubmit={handleSubmit} className="flex flex-col gap-3">
        {/* Input Area */}
        <div className={`relative flex items-end gap-2 sm:gap-3 p-2 sm:p-3 border rounded-xl transition-all duration-200 ${
          isFocused
            ? 'border-indigo-300 bg-indigo-50/30 shadow-sm'
            : 'border-gray-200 bg-gray-50/50'
        }`}>
          {/* Textarea */}
          <div className="flex-1">
            <Textarea
              ref={textareaRef}
              value={userInput}
              onChange={(e) => setUserInput(e.target.value)}
              onKeyDown={handleKeyDown}
              onFocus={() => setIsFocused(true)}
              onBlur={() => setIsFocused(false)}
              placeholder={placeholder}
              disabled={disabled}
              className="min-h-[40px] max-h-[120px] resize-none border-0 bg-transparent p-0 text-sm placeholder:text-gray-500 focus-visible:ring-0 focus-visible:ring-offset-0"
              style={{ 
                height: 'auto',
                overflow: 'hidden'
              }}
            />
          </div>

          {/* Send Button */}
          <Button
            type="submit"
            size="sm"
            disabled={!canSubmit}
            className={`flex-shrink-0 h-8 w-8 p-0 transition-all duration-200 ${
              canSubmit
                ? 'bg-indigo-600 hover:bg-indigo-700 text-white'
                : 'bg-gray-200 text-gray-400 cursor-not-allowed'
            }`}
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Send className="h-4 w-4" />
            )}
          </Button>
        </div>

        {/* Helper Text */}
        <div className="flex items-center justify-between text-xs text-gray-500">
          <span>
            Press Enter to send, Shift+Enter for new line
          </span>
          <span className={`${
            userInput.length > 500 ? 'text-amber-600' : 
            userInput.length > 800 ? 'text-red-600' : 'text-gray-400'
          }`}>
            {userInput.length}/1000
          </span>
        </div>
      </form>
    </div>
  );
};

export default ChatInput;
